import { useState } from "react";
import { GripVertical, MoreVertical, Check } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useChat } from "@/hooks/use-chat";
import { formatDistanceToNow } from "date-fns";
import type { ChatSession } from "@shared/schema";

interface ChatSessionItemProps {
  session: ChatSession;
  isSelected: boolean;
  isCurrent: boolean;
  onSelect: (sessionId: string) => void;
  onToggleSelect: (sessionId: string) => void;
}

export function ChatSessionItem({
  session,
  isSelected,
  isCurrent,
  onSelect,
  onToggleSelect,
}: ChatSessionItemProps) {
  const { updateSession, deleteSession } = useChat();

  const handleRename = async () => {
    const newTitle = prompt("Enter new title:", session.title);
    if (newTitle && newTitle !== session.title) {
      await updateSession(session.id, { title: newTitle });
    }
  };

  const handleDelete = async () => {
    if (confirm(`Are you sure you want to delete "${session.title}"?`)) {
      await deleteSession(session.id);
    }
  };

  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("text/plain", session.id);
    e.dataTransfer.effectAllowed = "move";
  };

  const timeAgo = formatDistanceToNow(new Date(session.lastMessageAt), { addSuffix: true });

  return (
    <div
      className={`chat-session-item bg-card/50 hover:bg-card/80 rounded-lg p-3 cursor-pointer transition-all duration-200 border border-border/50 ${
        isCurrent ? "bg-primary/10 border-primary/30" : ""
      }`}
      onClick={() => onSelect(session.id)}
      draggable
      onDragStart={handleDragStart}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 flex-1">
          <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab active:cursor-grabbing" />
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onToggleSelect(session.id)}
            onClick={(e) => e.stopPropagation()}
            className="border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary"
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-sm font-medium text-foreground truncate">{session.title}</h3>
              {isCurrent && (
                <span className="text-xs bg-primary/20 text-primary px-2 py-0.5 rounded">
                  Current
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{session.messageCount} messages</span>
              <span>•</span>
              <span>{timeAgo}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                className="p-1 h-auto text-muted-foreground hover:text-foreground"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              onClick={(e) => e.stopPropagation()}
            >
              <DropdownMenuItem onClick={handleRename}>Rename</DropdownMenuItem>
              <DropdownMenuItem onClick={handleDelete} className="text-red-500">
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
