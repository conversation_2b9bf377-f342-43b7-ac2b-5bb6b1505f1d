import { useState, useRef, useEffect } from "react";
import { Send } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useIsMobile } from "@/hooks/use-mobile";
import { FeedbackButton } from "./feedback-button";

interface MessageInputProps {
  onSendMessage: (content: string) => void;
  isLoading: boolean;
  sessionId: string;
}

export function MessageInput({ onSendMessage, isLoading, sessionId }: MessageInputProps) {
  const [message, setMessage] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const isMobile = useIsMobile();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message.trim());
      setMessage("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Enter creates new line, Ctrl/Cmd+Enter sends message
    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit(e);
    }
    // Regular Enter just creates a new line (default behavior)
  };

  useEffect(() => {
    if (textareaRef.current) {
      // Reset height to auto to get the correct scrollHeight
      textareaRef.current.style.height = "auto";
      // Set height with min of 40px (about 2 lines) and max of 160px (about 8 lines)
      const newHeight = Math.max(40, Math.min(textareaRef.current.scrollHeight, 160));
      textareaRef.current.style.height = `${newHeight}px`;
    }
  }, [message]);

  return (
    <div className="p-4 border-t border-border bg-background">
      <div className="max-w-4xl mx-auto">
        <form onSubmit={handleSubmit} className="relative">
          <div className="bg-card rounded-2xl shadow-lg border border-border p-4">
            <div className="flex items-end gap-3">
              <div className="flex-1">
                <Textarea
                  ref={textareaRef}
                  placeholder="Type your message... (Ctrl+Enter to send)"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="w-full resize-none border-0 outline-none bg-transparent text-foreground placeholder:text-muted-foreground text-sm min-h-[40px] max-h-[160px] p-0 focus-visible:ring-0 transition-all duration-200"
                  rows={1}
                  disabled={isLoading}
                  style={{ height: '40px' }}
                />
              </div>
              <div className="flex items-center gap-2">
                <Button
                  type="submit"
                  size="sm"
                  disabled={!message.trim() || isLoading}
                  className="p-2"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-end mt-2 pt-2 border-t border-border">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <FeedbackButton />
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
