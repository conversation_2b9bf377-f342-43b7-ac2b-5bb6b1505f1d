import { useState } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "./ui/dialog";
import { Textarea } from "./ui/textarea";
import { useToast } from "@/hooks/use-toast";

export function FeedbackButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [error, setError] = useState("");
  const { toast } = useToast();

  const handleSendFeedback = async () => {
    if (feedback.trim().length === 0) {
      setError("Feedback cannot be empty.");
      return;
    }
    setError("");
    try {
      const response = await fetch("/api/feedback", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ message: feedback }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Feedback sent successfully!",
        });
        setIsOpen(false);
        setFeedback("");
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Failed to send feedback.");
      }
    } catch (error) {
      setError("An unexpected error occurred.");
    }
  };

  return (
    <>
      <Button
        variant="link"
        className="text-xs text-muted-foreground"
        onClick={() => setIsOpen(true)}
      >
        Want to give me Feedback? klick here
      </Button>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Provide Feedback</DialogTitle>
          </DialogHeader>
          <Textarea
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            maxLength={500}
            placeholder="Type your feedback here..."
          />
          <p className="text-xs text-muted-foreground text-right">
            {feedback.length} / 500
          </p>
          {error && <p className="text-red-500 text-sm">{error}</p>}
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button onClick={handleSendFeedback}>Send</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 