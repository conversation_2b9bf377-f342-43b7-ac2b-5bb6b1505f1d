import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { chatApi } from "@/lib/chat-api";
import { useToast } from "@/hooks/use-toast";
import type { InsertFolder } from "@shared/schema";

// API base path for production
const API_BASE = import.meta.env.PROD ? "/jurbot-chat/api" : "/api";

export function useFolders() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Get all folders
  const { data: folders = [], isLoading } = useQuery({
    queryKey: [`${API_BASE}/folders`],
    queryFn: () => chatApi.getFolders(),
  });

  // Create folder
  const createFolderMutation = useMutation({
    mutationFn: chatApi.createFolder,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/folders`] });
      toast({
        title: "Folder created",
        description: "New folder has been created successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create folder.",
        variant: "destructive",
      });
    },
  });

  // Update folder
  const updateFolderMutation = useMutation({
    mutationFn: ({ id, folder }: { id: number; folder: Partial<InsertFolder> }) =>
      chatApi.updateFolder(id, folder),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/folders`] });
      toast({
        title: "Folder updated",
        description: "Folder has been updated successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update folder.",
        variant: "destructive",
      });
    },
  });

  // Delete folder
  const deleteFolderMutation = useMutation({
    mutationFn: chatApi.deleteFolder,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/folders`] });
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/chat-sessions`] });
      toast({
        title: "Folder deleted",
        description: "Folder has been deleted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete folder.",
        variant: "destructive",
      });
    },
  });

  return {
    folders,
    isLoading,
    createFolder: createFolderMutation.mutateAsync,
    updateFolder: (id: number, folder: Partial<InsertFolder>) =>
      updateFolderMutation.mutateAsync({ id, folder }),
    deleteFolder: deleteFolderMutation.mutateAsync,
  };
}
