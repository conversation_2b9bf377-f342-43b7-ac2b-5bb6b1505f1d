import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { chatApi } from "@/lib/chat-api";
import { useToast } from "@/hooks/use-toast";
import type { InsertChatSession, UpdateChatSession, Message } from "@shared/schema";

// API base path for production
const API_BASE = import.meta.env.PROD ? "/jurbot-chat/api" : "/api";

export function useChat(sessionId?: string | null) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Get all chat sessions
  const { data: chatSessions = [], isLoading: isLoadingSessions } = useQuery({
    queryKey: [`${API_BASE}/chat-sessions`],
    queryFn: () => chatApi.getChatSessions(),
  });

  // Get messages for a specific session
  const { data: messages = [], isLoading: isLoadingMessages } = useQuery({
    queryKey: [`${API_BASE}/chat-sessions`, sessionId, "messages"],
    queryFn: () => sessionId ? chatApi.getMessages(sessionId) : Promise.resolve([]),
    enabled: !!sessionId,
  });

  // Create chat session
  const createSessionMutation = useMutation({
    mutationFn: chatApi.createChatSession,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/chat-sessions`] });
      toast({
        title: "Chat session created",
        description: "New chat session has been created successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create chat session.",
        variant: "destructive",
      });
    },
  });

  // Update chat session
  const updateSessionMutation = useMutation({
    mutationFn: ({ id, session }: { id: string; session: UpdateChatSession }) =>
      chatApi.updateChatSession(id, session),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/chat-sessions`] });
      toast({
        title: "Chat session updated",
        description: "Chat session has been updated successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update chat session.",
        variant: "destructive",
      });
    },
  });

  // Delete chat session
  const deleteSessionMutation = useMutation({
    mutationFn: chatApi.deleteChatSession,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/chat-sessions`] });
      toast({
        title: "Chat session deleted",
        description: "Chat session has been deleted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete chat session.",
        variant: "destructive",
      });
    },
  });

  // Move chat session
  const moveSessionMutation = useMutation({
    mutationFn: ({ sessionId, folderId }: { sessionId: string; folderId: number | null }) =>
      chatApi.moveChatSession(sessionId, folderId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/chat-sessions`] });
      toast({
        title: "Chat session moved",
        description: "Chat session has been moved successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to move chat session.",
        variant: "destructive",
      });
    },
  });

  // Send message
  const sendMessageMutation = useMutation({
    mutationFn: ({ sessionId, content }: { sessionId: string; content: string }) =>
      chatApi.sendMessage(sessionId, content),
    onMutate: async ({ content }) => {
      if (!sessionId) return;

      const queryKey = [`${API_BASE}/chat-sessions`, sessionId, "messages"];

      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey });

      // Snapshot the previous value
      const previousMessages = queryClient.getQueryData<Message[]>(queryKey);

      // Optimistically update to the new value
      queryClient.setQueryData<Message[]>(queryKey, (old = []) => {
        const optimisticUserMessage = {
          id: Date.now(),
          sessionId: sessionId,
          content: content,
          role: 'user',
          timestamp: new Date().toISOString(),
        };
        return [...old, optimisticUserMessage as unknown as Message];
      });

      // Return a context object with the snapshotted value
      return { previousMessages, queryKey };
    },
    onError: (err, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMessages) {
        queryClient.setQueryData(context.queryKey, context.previousMessages);
      }
      toast({
        title: "Error sending message",
        description: "Please try again.",
        variant: "destructive",
      });
    },
    onSettled: (_data, _error, _variables, context) => {
      if (!sessionId) return;
      // Invalidate messages to refetch from server and get real data
      if (context?.queryKey) {
        queryClient.invalidateQueries({ queryKey: context.queryKey });
      }
      // Invalidate sessions to update message count, etc.
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/chat-sessions`] });
    },
  });

  // Clear messages
  const clearMessagesMutation = useMutation({
    mutationFn: chatApi.clearMessages,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/chat-sessions`, sessionId, "messages"] });
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/chat-sessions`] });
      toast({
        title: "Messages cleared",
        description: "All messages have been cleared successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to clear messages.",
        variant: "destructive",
      });
    },
  });

  return {
    chatSessions,
    messages,
    isLoading: isLoadingSessions || isLoadingMessages || sendMessageMutation.isPending,
    createSession: createSessionMutation.mutateAsync,
    updateSession: (id: string, session: UpdateChatSession) =>
      updateSessionMutation.mutateAsync({ id, session }),
    deleteSession: deleteSessionMutation.mutateAsync,
    moveSession: (sessionId: string, folderId: number | null) =>
      moveSessionMutation.mutateAsync({ sessionId, folderId }),
    sendMessage: (content: string) => {
      if (sessionId) {
        return sendMessageMutation.mutateAsync({ sessionId, content });
      }
      return Promise.reject(new Error("No session selected"));
    },
    clearMessages: () => {
      if (sessionId) {
        return clearMessagesMutation.mutateAsync(sessionId);
      }
      return Promise.reject(new Error("No session selected"));
    },
  };
}
