import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';

interface User {
  email: string;
  username: string;
  role: string;
  [key: string]: any;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  login: (token: string) => void;
  logout: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const logout = useCallback(() => {
    // Prevent redirect loops if logout is already in progress
    if (isLoggingOut) return;

    setIsLoggingOut(true);
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('authToken');
    // Use environment variable for logout URL, with a fallback for local dev
    const logoutUrl = import.meta.env.VITE_LOGOUT_URL || '/login-redirect-loop-test';
    window.location.href = logoutUrl;
  }, [isLoggingOut]);

  const login = useCallback(async (token: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(import.meta.env.PROD ? '/jurbot-chat/api/auth/verify' : '/api/auth/verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token }),
      });

      if (response.ok) {
        const { user: userData } = await response.json();
        setUser(userData);
        setIsAuthenticated(true);
        localStorage.setItem('authToken', token);
        setIsLoggingOut(false); // Reset on successful login
      } else {
        logout();
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  }, [logout]);

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 