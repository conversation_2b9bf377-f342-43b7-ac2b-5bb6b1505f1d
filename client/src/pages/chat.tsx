import { useState, useEffect, useCallback } from "react";
import { Menu } from "lucide-react";
import { Sidebar } from "@/components/sidebar";
import { ChatArea } from "@/components/chat-area";
import { useChat } from "@/hooks/use-chat";
import { useFolders } from "@/hooks/use-folders";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";

export default function Chat() {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [selectedChats, setSelectedChats] = useState<string[]>([]);
  const [showWebhookConfig, setShowWebhookConfig] = useState(false);
  const [isCreatingNewChat, setIsCreatingNewChat] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const { createSession, chatSessions, deleteSession, isLoading: isLoadingSessions } = useChat();
  const { folders } = useFolders();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [isSidebarOpen, setIsSidebarOpen] = useState(!isMobile);

  useEffect(() => {
    setIsSidebarOpen(!isMobile);
  }, [isMobile]);

  const handleNewChat = useCallback(async () => {
    // Prevent multiple concurrent creations
    if (isCreatingNewChat) return;

    setIsCreatingNewChat(true);
    const sessionId = `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      await createSession({
        id: sessionId,
        title: "New Chat",
        folderId: null,
      });
      setCurrentSessionId(sessionId);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create a new chat session.",
        variant: "destructive",
      });
      console.error("Failed to create new chat session", error);
    } finally {
      setIsCreatingNewChat(false);
    }
  }, [createSession, isCreatingNewChat, toast]);

  const handleSelectChat = (sessionId: string) => {
    setCurrentSessionId(sessionId);
  };

  const handleToggleSelect = (sessionId: string) => {
    setSelectedChats(prev => 
      prev.includes(sessionId) 
        ? prev.filter(id => id !== sessionId)
        : [...prev, sessionId]
    );
  };

  const handleSelectAll = () => {
    if (selectedChats.length === chatSessions.length) {
      setSelectedChats([]);
    } else {
      setSelectedChats(chatSessions.map(session => session.id));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedChats.length === 0) return;
    
    if (confirm(`Are you sure you want to delete ${selectedChats.length} chat session(s)?`)) {
      try {
        for (const sessionId of selectedChats) {
          await deleteSession(sessionId);
        }
        
        // If current session was deleted, clear it
        if (currentSessionId && selectedChats.includes(currentSessionId)) {
          setCurrentSessionId(null);
        }
        
        setSelectedChats([]);
        toast({
          title: "Success",
          description: `Deleted ${selectedChats.length} chat session(s)`,
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete some chat sessions",
          variant: "destructive",
        });
      }
    }
  };

  // Select the latest session if available, but don't auto-create
  useEffect(() => {
    if (isLoadingSessions) {
      return;
    }

    // If there are chats, but none is selected, select the most recent one.
    if (chatSessions.length > 0 && !currentSessionId) {
      const sortedSessions = [...chatSessions].sort((a, b) =>
        new Date(b.lastMessageAt).getTime() - new Date(a.lastMessageAt).getTime()
      );
      setCurrentSessionId(sortedSessions[0].id);
    }
    // If there are no chats, clear the current session
    else if (chatSessions.length === 0) {
      setCurrentSessionId(null);
    }
  }, [
    isLoadingSessions,
    chatSessions,
    currentSessionId
  ]);

  return (
    <div className="h-screen overflow-hidden bg-gradient-primary">
      <div className="flex h-full backdrop-blur-sm">
        {isSidebarOpen && (
          <Sidebar
            currentSessionId={currentSessionId}
            selectedChats={selectedChats}
            onNewChat={handleNewChat}
            onSelectChat={handleSelectChat}
            onToggleSelect={handleToggleSelect}
            onSelectAll={handleSelectAll}
            onBulkDelete={handleBulkDelete}
            onWebhookConfig={() => setShowWebhookConfig(true)}
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onClose={() => setIsSidebarOpen(false)}
          />
        )}
        <ChatArea 
          sessionId={currentSessionId}
          startDate={startDate}
          endDate={endDate}
          showWebhookConfig={showWebhookConfig}
          onCloseWebhookConfig={() => setShowWebhookConfig(false)}
          onMenuClick={() => setIsSidebarOpen(true)}
        />
      </div>
    </div>
  );
}
