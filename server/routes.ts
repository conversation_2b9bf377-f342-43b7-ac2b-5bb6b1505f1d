import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertFolderSchema, insertChatSessionSchema, insertMessageSchema, updateChatSessionSchema } from "@shared/schema";
import { z } from "zod";
import { sendFeedbackEmail } from "./feedback";

const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL || "https://n8n.sollution.ai/webhook/e98745cf-eea7-4ee3-86ba-54wezrdth";

export async function registerRoutes(app: Express): Promise<Server> {
  // Configuration endpoint
  app.get("/api/config", (req, res) => {
    res.json({
      webhookUrl: N8N_WEBHOOK_URL,
      hasWebhookConfigured: !!process.env.N8N_WEBHOOK_URL,
    });
  });

  // Test webhook endpoint
  app.post("/api/test-webhook", async (req, res) => {
    try {
      const { webhookUrl, message } = req.body;
      const testUrl = webhookUrl || N8N_WEBHOOK_URL;
      const testMessage = message || "Hello, this is a test message from the chat application";
      
      console.log(`[webhook-test] ===== TEST REQUEST =====`);
      console.log(`[webhook-test] Method: POST`);
      console.log(`[webhook-test] URL: ${testUrl}`);
      console.log(`[webhook-test] Body:`, JSON.stringify({
        sessionId: "test-session",
        message: testMessage,
      }, null, 2));
      
      const testResponse = await fetch(testUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "ChatApp/1.0",
          "Accept": "application/json, text/plain, */*",
        },
        body: JSON.stringify({
          sessionId: "test-session",
          message: testMessage,
        }),
      });
      
      console.log(`[webhook-test] ===== TEST RESPONSE =====`);
      console.log(`[webhook-test] Status: ${testResponse.status} ${testResponse.statusText}`);
      console.log(`[webhook-test] Headers:`, Object.fromEntries(testResponse.headers.entries()));
      
      if (!testResponse.ok) {
        const errorText = await testResponse.text();
        console.error(`[webhook-test] Error: ${errorText}`);
        return res.status(400).json({ 
          success: false, 
          error: `Webhook test failed: ${testResponse.status} - ${errorText}` 
        });
      }
      
      const responseText = await testResponse.text();
      console.log(`[webhook-test] Raw response: ${responseText}`);
      
      let responseData: any = {};
      if (responseText.trim()) {
        try {
          responseData = JSON.parse(responseText);
        } catch (parseError) {
          responseData = { response: responseText };
        }
      }
      
      res.json({ 
        success: true, 
        message: "Webhook test successful!", 
        response: responseData,
        rawResponse: responseText 
      });
    } catch (error) {
      console.error(`[webhook-test] Error:`, error);
      res.status(500).json({ 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error" 
      });
    }
  });

  // Feedback endpoint
  app.post("/api/feedback", async (req, res) => {
    try {
      const { message } = z.object({ message: z.string().min(1).max(500) }).parse(req.body);
      await sendFeedbackEmail(message);
      res.status(200).json({ success: true, message: "Feedback sent successfully!" });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ success: false, error: "Invalid feedback data", details: error.errors });
      }
      console.error("Feedback submission error:", error);
      res.status(500).json({ success: false, error: "Failed to send feedback." });
    }
  });

  // Folders
  app.get("/api/folders", async (req, res) => {
    try {
      const folders = await storage.getFolders();
      res.json(folders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch folders" });
    }
  });

  app.post("/api/folders", async (req, res) => {
    try {
      const folderData = insertFolderSchema.parse(req.body);
      const folder = await storage.createFolder(folderData);
      res.json(folder);
    } catch (error) {
      res.status(400).json({ message: "Invalid folder data" });
    }
  });

  app.put("/api/folders/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const folderData = insertFolderSchema.partial().parse(req.body);
      const folder = await storage.updateFolder(id, folderData);
      
      if (!folder) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      res.json(folder);
    } catch (error) {
      res.status(400).json({ message: "Invalid folder data" });
    }
  });

  app.delete("/api/folders/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteFolder(id);
      
      if (!success) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete folder" });
    }
  });

  // Chat Sessions
  app.get("/api/chat-sessions", async (req, res) => {
    try {
      const sessions = await storage.getChatSessions();
      res.json(sessions);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch chat sessions" });
    }
  });

  app.post("/api/chat-sessions", async (req, res) => {
    try {
      const sessionData = insertChatSessionSchema.parse(req.body);
      const session = await storage.createChatSession(sessionData);
      res.json(session);
    } catch (error) {
      res.status(400).json({ message: "Invalid session data" });
    }
  });

  app.put("/api/chat-sessions/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const sessionData = updateChatSessionSchema.parse(req.body);
      const session = await storage.updateChatSession(id, sessionData);
      
      if (!session) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json(session);
    } catch (error) {
      res.status(400).json({ message: "Invalid session data" });
    }
  });

  app.delete("/api/chat-sessions/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const success = await storage.deleteChatSession(id);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete chat session" });
    }
  });

  app.post("/api/chat-sessions/:id/move", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const { folderId } = z.object({ folderId: z.number().nullable() }).parse(req.body);
      
      const success = await storage.moveChatSessionToFolder(sessionId, folderId);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(400).json({ message: "Invalid move data" });
    }
  });

  // Messages
  app.get("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const messages = await storage.getMessages(sessionId);
      res.json(messages);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  app.post("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const messageData = insertMessageSchema.parse({
        ...req.body,
        sessionId,
      });
      
      // Create user message
      const userMessage = await storage.createMessage(messageData);
      
      // Send to n8n webhook
      try {
        console.log(`[webhook] ===== WEBHOOK REQUEST =====`);
        console.log(`[webhook] Method: POST`);
        console.log(`[webhook] URL: ${N8N_WEBHOOK_URL}`);
        console.log(`[webhook] Headers:`, {
          "Content-Type": "application/json",
          "User-Agent": "ChatApp/1.0",
          "Accept": "application/json, text/plain, */*"
        });
        console.log(`[webhook] Body:`, JSON.stringify({
          sessionId,
          message: messageData.content,
        }, null, 2));
        
        const webhookResponse = await fetch(N8N_WEBHOOK_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "User-Agent": "ChatApp/1.0",
            "Accept": "application/json, text/plain, */*",
          },
          body: JSON.stringify({
            sessionId,
            message: messageData.content,
          }),
        });
        
        console.log(`[webhook] ===== WEBHOOK RESPONSE =====`);
        console.log(`[webhook] Status: ${webhookResponse.status} ${webhookResponse.statusText}`);
        console.log(`[webhook] Headers:`, Object.fromEntries(webhookResponse.headers.entries()));
        
        if (!webhookResponse.ok) {
          const errorText = await webhookResponse.text();
          console.error(`[webhook] Error response body: ${errorText}`);
          throw new Error(`Webhook failed with status ${webhookResponse.status}: ${errorText}`);
        }
        
        let webhookData: any = {};
        const responseText = await webhookResponse.text();
        console.log(`[webhook] Raw response:`, responseText);
        
        if (responseText.trim()) {
          try {
            webhookData = JSON.parse(responseText);
            console.log(`[webhook] Parsed response:`, webhookData);
          } catch (parseError) {
            console.log(`[webhook] Non-JSON response, using as plain text`);
            webhookData = { response: responseText };
          }
        }
        
        // Create AI response message
        let aiResponseContent: string;
        
        if (webhookData.output) {
          // Handle n8n response with "output" field
          aiResponseContent = webhookData.output;
        } else if (webhookData.response || webhookData.message || responseText) {
          // Handle other response formats
          aiResponseContent = webhookData.response || webhookData.message || responseText;
        } else {
          aiResponseContent = `Hello! I see you said "${messageData.content}".

⚠️ **n8n Configuration Issue**: Your webhook is receiving requests but returning empty responses. 

To fix this, check your n8n workflow:
1. Make sure the workflow is **Active** (green toggle)
2. Add a "Respond to Webhook" node at the end
3. Configure it to return: {"response": "Your AI response here"}

Once fixed, I'll provide proper AI responses!`;
        }
        
        const aiMessage = await storage.createMessage({
          sessionId,
          content: aiResponseContent,
          role: "assistant",
        });
        
        res.json({ userMessage, aiMessage });
      } catch (webhookError) {
        console.error(`[webhook] Error:`, webhookError);
        
        // Create fallback AI message with more specific error info
        const errorMessage = webhookError instanceof Error 
          ? `AI service error: ${webhookError.message}` 
          : "I'm having trouble connecting to the AI service right now. Please try again later.";
          
        const aiMessage = await storage.createMessage({
          sessionId,
          content: errorMessage,
          role: "assistant",
        });
        
        res.json({ userMessage, aiMessage });
      }
    } catch (error) {
      res.status(400).json({ message: "Invalid message data" });
    }
  });

  app.delete("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const success = await storage.deleteMessages(sessionId);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to clear messages" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
