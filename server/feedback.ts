import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";

const sesClient = new SESClient({
  region: process.env.AWS_REGION || "eu-central-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function sendFeedbackEmail(message: string): Promise<void> {
  const command = new SendEmailCommand({
    Source: process.env.SES_FROM_EMAIL || "<EMAIL>",
    Destination: {
      ToAddresses: ["<EMAIL>"],
    },
    Message: {
      Subject: {
        Data: "User Feedback for JURBOT",
        Charset: "UTF-8",
      },
      Body: {
        Text: {
          Data: message,
          Charset: "UTF-8",
        },
      },
    },
  });

  try {
    await sesClient.send(command);
  } catch (error) {
    console.error("Failed to send feedback email:", error);
    throw new Error("Failed to send feedback via email.");
  }
} 